use std::future::{ready, Ready};
use std::rc::Rc;
use actix_web::{
    dev::{forward_ready, Service, ServiceRequest, ServiceResponse, Transform},
    Error,
};
use futures_util::future::LocalBoxFuture;
use log::debug;

/// CORS响应头中间件
///
/// 此中间件用于统一给所有HTTP响应添加CORS头部
/// 解决跨域访问问题
pub struct CorsHeaders;

impl Default for CorsHeaders {
    fn default() -> Self {
        CorsHeaders
    }
}

// 中间件工厂实现
impl<S, B> Transform<S, ServiceRequest> for CorsHeaders
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type InitError = ();
    type Transform = CorsHeadersMiddleware<S>;
    type Future = Ready<Result<Self::Transform, Self::InitError>>;

    fn new_transform(&self, service: S) -> Self::Future {
        ready(Ok(CorsHeadersMiddleware {
            service: Rc::new(service),
        }))
    }
}

/// CORS响应头中间件服务
pub struct CorsHeadersMiddleware<S> {
    service: Rc<S>,
}

impl<S, B> Service<ServiceRequest> for CorsHeadersMiddleware<S>
where
    S: Service<ServiceRequest, Response = ServiceResponse<B>, Error = Error> + 'static,
    S::Future: 'static,
    B: 'static,
{
    type Response = ServiceResponse<B>;
    type Error = Error;
    type Future = LocalBoxFuture<'static, Result<Self::Response, Self::Error>>;

    forward_ready!(service);

    fn call(&self, req: ServiceRequest) -> Self::Future {
        let service = self.service.clone();

        Box::pin(async move {
            let path = req.path().to_string();
            debug!("处理请求路径: {}", path);

            // 调用下一个服务
            let mut res = service.call(req).await?;

            // 获取响应头的可变引用
            let headers = res.headers_mut();

            // 添加CORS头部
            headers.insert(
                actix_web::http::header::ACCESS_CONTROL_ALLOW_ORIGIN,
                actix_web::http::HeaderValue::from_static("*")
            );
            headers.insert(
                actix_web::http::header::ACCESS_CONTROL_ALLOW_METHODS,
                actix_web::http::HeaderValue::from_static("GET, POST, PUT, DELETE, OPTIONS")
            );
            headers.insert(
                actix_web::http::header::ACCESS_CONTROL_ALLOW_HEADERS,
                actix_web::http::HeaderValue::from_static("Content-Type, Authorization, X-Requested-With")
            );
            headers.insert(
                actix_web::http::header::ACCESS_CONTROL_MAX_AGE,
                actix_web::http::HeaderValue::from_static("86400")
            );

            debug!("已添加CORS头部到响应");

            Ok(res)
        })
    }
}
