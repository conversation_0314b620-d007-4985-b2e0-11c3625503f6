use actix_web::{App, HttpServer, web};
use actix_cors::Cors;
use listenfd::ListenFd;
use log::{debug, error, info};
use std::sync::Arc;

// 导入模块
mod config;
mod db;
mod dto;
mod enums;
mod handlers;
mod middleware;
mod models;
mod routes;
mod schedule;
mod services;
mod utils;

#[cfg(test)]
mod tests;

use config::AppConfig;
use db::DbConnections;

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    // 加载配置
    let config = AppConfig::new();

    // 初始化日志系统
    env_logger::init_from_env(env_logger::Env::new().default_filter_or(&config.log_level));

    // 打印应用信息
    info!("启动应用: {} v{}", config.app_name, config.app_version);
    debug!("环境: {}, 调试模式: {}", config.environment, config.debug);

    // 初始化数据库连接
    let db_connections = match DbConnections::new(&config).await {
        Ok(connections) => {
            info!("数据库连接初始化成功");
            // 检查连接是否正常
            if let Err(e) = connections.check_connections().await {
                error!("数据库连接检查失败: {}", e);
                None
            } else {
                info!("数据库连接检查成功");

                // 初始化数据库表结构
                if let Err(e) = connections.initialize_schema(&config).await {
                    error!("数据库表结构初始化失败: {}", e);
                    // 表结构初始化失败不影响应用启动
                } else {
                    info!("数据库表结构初始化成功");
                }

                Some(connections)
            }
        }
        Err(e) => {
            error!("数据库连接初始化失败: {}", e);
            None
        }
    };

    // 打印服务器信息
    let addr = config.server_addr();
    info!("启动服务器在 http://{}:{}", addr.ip(), addr.port());



    // 创建OSS客户端
    let oss_client = {
        match utils::OssClient::new(&config.oss) {
            client => {
                info!("OSS客户端初始化成功");
                Some(Arc::new(client))
            }
        }
    };

    // 创建MNS客户端
    let mns_client = {
        match utils::MnsClient::new(config.mns.clone()) {
            client => {
                info!("MNS客户端初始化成功");
                Some(Arc::new(client))
            }
        }
    };

    // 创建MySQL服务
    let (mysql_user_service, mysql_favorite_service, mysql_tag_service, mysql_bookmark_service, mysql_app_version_service, mysql_note_service, mysql_note_draft_service, augment_user_service, mysql_ai_usage_service, admin_user_service) = if let Some(db_conn) = &db_connections {
        // 创建MySQL用户服务
        let mysql_user_service = match services::mysql::MySqlUserService::new(db_conn, &config) {
            Ok(service) => {
                info!("MySQL用户服务初始化成功");
                Some(Arc::new(service))
            },
            Err(e) => {
                error!("MySQL用户服务初始化失败: {}", e);
                None
            }
        };

        // 创建MySQL收藏夹服务
        let mysql_favorite_service = match services::mysql::MySqlFavoriteService::new(db_conn, &config) {
            Ok(service) => {
                info!("MySQL收藏夹服务初始化成功");
                Some(Arc::new(service))
            },
            Err(e) => {
                error!("MySQL收藏夹服务初始化失败: {}", e);
                None
            }
        };

        // 创建MySQL标签服务
        let mysql_tag_service = match services::mysql::MySqlTagService::new(db_conn, &config) {
            Ok(service) => {
                info!("MySQL标签服务初始化成功");
                Some(Arc::new(service))
            },
            Err(e) => {
                error!("MySQL标签服务初始化失败: {}", e);
                None
            }
        };

        // 创建MySQL书签服务
        let mysql_bookmark_service = match services::mysql::MySqlBookmarkService::new(db_conn, &config, mysql_favorite_service.clone(), mysql_tag_service.clone()) {
            Ok(service) => {
                info!("MySQL书签服务初始化成功");
                Some(Arc::new(service))
            },
            Err(e) => {
                error!("MySQL书签服务初始化失败: {}", e);
                None
            }
        };

        // 创建MySQL应用版本服务
        let mysql_app_version_service = match services::mysql::MySqlAppVersionService::new(db_conn, &config) {
            Ok(service) => {
                info!("MySQL应用版本服务初始化成功");
                Some(Arc::new(service))
            },
            Err(e) => {
                error!("MySQL应用版本服务初始化失败: {}", e);
                None
            }
        };

        // 创建MySQL笔记服务
        let mysql_note_service = match services::mysql::MySqlNoteService::new(db_conn, &config) {
            Ok(service) => {
                info!("MySQL笔记服务初始化成功");
                Some(Arc::new(service))
            },
            Err(e) => {
                error!("MySQL笔记服务初始化失败: {}", e);
                None
            }
        };

        // 创建MySQL笔记草稿服务
        let mysql_note_draft_service = if let Some(mysql_client) = &db_conn.mysql {
            let pool = Arc::new(mysql_client.pool().clone());
            let service = services::mysql::MySqlNoteDraftService::new(pool);
            info!("MySQL笔记草稿服务初始化成功");
            Some(Arc::new(service))
        } else {
            error!("MySQL连接不可用，无法创建笔记草稿服务");
            None
        };

        // 创建Augment用户服务
        let augment_user_service = if let Some(mysql_client) = &db_conn.mysql {
            let pool = Arc::new(mysql_client.pool().clone());
            let service = services::mysql::MySqlAugmentUserService::new(pool);
            info!("Augment用户服务初始化成功");
            Some(Arc::new(service))
        } else {
            error!("MySQL连接不可用，无法创建Augment用户服务");
            None
        };

        // 创建AI使用次数服务
        let mysql_ai_usage_service = if let Some(mysql_client) = &db_conn.mysql {
            let pool = mysql_client.pool().clone();
            let service = services::mysql::MySqlAiUsageService::new(pool);
            info!("MySQL AI使用次数服务初始化成功");
            Some(Arc::new(service))
        } else {
            error!("MySQL连接不可用，无法创建AI使用次数服务");
            None
        };

        // 创建MySQL后台管理用户服务
        let admin_user_service = if let Some(mysql_client) = &db_conn.mysql {
            let pool = mysql_client.pool().clone();
            let service = services::mysql::MySqlAdminUserService::new(pool);
            info!("MySQL后台管理用户服务初始化成功");
            Some(Arc::new(service))
        } else {
            error!("MySQL连接不可用，无法创建后台管理用户服务");
            None
        };

        (mysql_user_service, mysql_favorite_service, mysql_tag_service, mysql_bookmark_service, mysql_app_version_service, mysql_note_service, mysql_note_draft_service, augment_user_service, mysql_ai_usage_service, admin_user_service)
    } else {
        (None, None, None, None, None, None, None, None, None, None)
    };

    // 创建微信服务
    let wechat_service = {
        let service = services::wechat::WechatService::new(&config);
        info!("微信服务初始化成功");
        Some(Arc::new(service))
    };

    // 创建腾讯云语音识别服务
    let tencent_asr_service = {
        let service = services::tencent_cloud::TencentAsrService::new(&config);
        info!("腾讯云语音识别服务初始化成功");
        Some(Arc::new(service))
    };

    // 创建应用状态
    let app_state_arc = Arc::new(AppState {
        config: Arc::clone(&config),
        db: db_connections,
        // MySQL服务
        mysql_user_service,
        mysql_favorite_service,
        mysql_tag_service,
        mysql_bookmark_service,
        mysql_app_version_service,
        mysql_note_service,
        mysql_note_draft_service,
        augment_user_service,
        mysql_ai_usage_service,
        admin_user_service,
        // 其他服务
        oss_client,
        mns_client,
        // 微信服务
        wechat_service,
        // 腾讯云语音识别服务
        tencent_asr_service,
    });

    // 启动定时任务
    let schedule_manager = schedule::ScheduleManager::new(app_state_arc.clone());
    schedule_manager.start_all().await;

    // 包装为 web::Data 用于 HTTP 服务
    let app_state = web::Data::from(app_state_arc);

    // 初始化并绑定路由
    let mut server = HttpServer::new(move || {
        App::new()
            // 注册全局中间件
            .wrap(
                Cors::permissive()
            ) // CORS 中间件，处理跨域请求
            .wrap(middleware::TokenParser::default())
            // 设置payload大小限制为50MB（腾讯云回调可能包含大量识别结果）
            .app_data(web::PayloadConfig::new(50 * 1024 * 1024))
            .app_data(app_state.clone())
            .configure(routes::configure_routes)
    });

    // 检查是否有listenfd传递的socket
    let mut listenfd = ListenFd::from_env();
    server = match listenfd.take_tcp_listener(0)? {
        Some(listener) => {
            info!("使用systemfd提供的socket进行热重载");
            server.listen(listener)?
        }
        None => {
            info!("使用普通绑定方式启动服务器");
            server.bind(addr)?
        }
    };

    // 启动服务器
    server.workers(config.workers).run().await
}

/// 应用状态结构体，用于在请求处理程序之间共享数据
#[derive(Clone)]
pub struct AppState {
    pub config: Arc<AppConfig>,
    pub db: Option<DbConnections>,
    // MySQL服务
    pub mysql_user_service: Option<Arc<services::mysql::MySqlUserService>>,
    pub mysql_favorite_service: Option<Arc<services::mysql::MySqlFavoriteService>>,
    pub mysql_tag_service: Option<Arc<services::mysql::MySqlTagService>>,
    pub mysql_bookmark_service: Option<Arc<services::mysql::MySqlBookmarkService>>,
    pub mysql_app_version_service: Option<Arc<services::mysql::MySqlAppVersionService>>,
    pub mysql_note_service: Option<Arc<services::mysql::MySqlNoteService>>,
    pub mysql_note_draft_service: Option<Arc<services::mysql::MySqlNoteDraftService>>,
    pub augment_user_service: Option<Arc<services::mysql::MySqlAugmentUserService>>,
    pub mysql_ai_usage_service: Option<Arc<services::mysql::MySqlAiUsageService>>,
    pub admin_user_service: Option<Arc<services::mysql::MySqlAdminUserService>>,
    // 其他服务
    pub oss_client: Option<Arc<utils::OssClient>>,
    pub mns_client: Option<Arc<utils::MnsClient>>,
    // 微信服务
    pub wechat_service: Option<Arc<services::wechat::WechatService>>,
    // 腾讯云语音识别服务
    pub tencent_asr_service: Option<Arc<services::tencent_cloud::TencentAsrService>>,
}
