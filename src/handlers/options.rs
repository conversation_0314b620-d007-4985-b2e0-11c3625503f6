use actix_web::{HttpResponse, Result};
use log::debug;

/// 处理 OPTIONS 预检请求
///
/// 请求参数：无
/// 返回数据：空响应，包含 CORS 头部
pub async fn handle() -> Result<HttpResponse> {
    debug!("处理 OPTIONS 预检请求");
    
    Ok(HttpResponse::Ok()
        .insert_header(("Access-Control-Allow-Origin", "*"))
        .insert_header(("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS"))
        .insert_header(("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With"))
        .insert_header(("Access-Control-Max-Age", "86400"))
        .finish())
}
