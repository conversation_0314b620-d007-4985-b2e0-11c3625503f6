use actix_web::{HttpRequest, HttpResponse, Responder, web};
use log::{error, info};

use crate::AppState;
use crate::dto::{ApiResponse, ErrorCode, user::{UserListParams, UserListResponse, UserResponse}};
use crate::services::mysql::MySqlUserServiceError;
use crate::middleware::token_parser::get_user_id_from_request;

/**
 * 获取用户列表处理函数
 *
 * 请求参数：
 * - page: 页码，从1开始，默认1
 * - page_size: 每页数量，默认20
 *
 * 返回数据格式：
 * {
 *   "code": 200,
 *   "message": "获取用户列表成功",
 *   "data": {
 *     "users": [
 *       {
 *         "id": 1,
 *         "phone": "13800138000",
 *         "nickname": "用户昵称",
 *         "avatar": "头像URL",
 *         "created_at": "2023-01-01T00:00:00Z",
 *         "updated_at": "2023-01-01T00:00:00Z"
 *       }
 *     ],
 *     "total": 100,
 *     "page": 1,
 *     "page_size": 20
 *   }
 * }
 *
 * @param data 应用状态数据
 * @param req HTTP请求
 * @param query 查询参数
 * @return HTTP 响应
 */
pub async fn handle(
    data: web::Data<AppState>,
    req: HttpRequest,
    query: web::Query<UserListParams>,
) -> impl Responder {
    // 验证用户登录状态
    let _user_id_str = match get_user_id_from_request(&req) {
        Some(id) => id,
        None => {
            error!("用户未登录");
            let error_code = ErrorCode::TokenInvalid;
            return HttpResponse::Unauthorized().json(ApiResponse {
                code: error_code.code(),
                data: serde_json::Value::Null,
                message: error_code.message().to_string(),
            });
        }
    };

    info!("获取用户列表，页码: {}, 每页数量: {}", query.page, query.page_size);

    // 获取MySQL用户服务
    let mysql_user_service = match &data.mysql_user_service {
        Some(service) => service,
        None => {
            error!("MySQL用户服务不可用");
            let error_code = ErrorCode::ServiceUnavailable;
            return HttpResponse::ServiceUnavailable().json(ApiResponse {
                code: error_code.code(),
                data: serde_json::Value::Null,
                message: error_code.message().to_string(),
            });
        }
    };

    // 获取用户列表
    match mysql_user_service.list_users(query.page, query.page_size).await {
        Ok((users, total)) => {
            info!("获取用户列表成功，共 {} 条", total);

            // 转换为响应格式
            let user_responses: Vec<UserResponse> = users
                .iter()
                .map(|user| UserResponse::from_mysql_model(user))
                .collect();

            let success_code = ErrorCode::Success;
            HttpResponse::Ok().json(ApiResponse {
                code: success_code.code(),
                data: UserListResponse {
                    users: user_responses,
                    total,
                    page: query.page,
                    page_size: query.page_size,
                },
                message: success_code.message().to_string(),
            })
        },
        Err(e) => {
            error!("获取用户列表失败: {}", e);
            let error_code = match e {
                MySqlUserServiceError::DatabaseError(_) => ErrorCode::DatabaseError,
                _ => ErrorCode::SystemError,
            };

            HttpResponse::InternalServerError().json(ApiResponse {
                code: error_code.code(),
                data: serde_json::Value::Null,
                message: error_code.message().to_string(),
            })
        }
    }
}
