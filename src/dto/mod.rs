// 导出所有 DTO 模块
pub mod response;
pub mod user;
pub mod error_code;
pub mod bookmark;
pub mod tag;
pub mod upload;
pub mod sts;
pub mod wechat;
pub mod app_version;
pub mod note;
pub mod note_draft;
pub mod material;
pub mod augment_user;
pub mod tencent_cloud;
pub mod task;
pub mod prompt;
pub mod system_prompt;
pub mod ai_usage;
pub mod admin_user;

// 重新导出常用的结构体，方便使用
pub use response::ApiResponse;
pub use user::{RegisterRequest, RegisterData, LoginRequest, LoginData, UserListParams, UserListResponse, UserResponse};
pub use error_code::ErrorCode;
pub use bookmark::{
    AddBookmarkRequest, BookmarkResponse, UpdateBookmarkRequest, UpdateBookmarkResponse,
    DeleteBookmarkRequest, DeleteBookmarkResponse, AddBookmarkTagsRequest, AddBookmarkTagsResponse,
    RemoveBookmarkTagsRequest, RemoveBookmarkTagsResponse, GetBookmarkTagsParams, GetBookmarkTagsResponse
};
// pub use tencent_cloud::{TencentAsrCallbackRequest, TencentAsrCallbackResponse};
pub use upload::UploadResponse;
// pub use sts::{StsCredentials, StsTokenResponse};
// pub use wechat::{WechatLoginRequest, WechatLoginData};
// pub use app_version::{VersionCheckResponse, VersionStoreRequest, VersionStoreResponse};
pub use note::{NoteDetailResponse, NoteDetailParams, NoteListParams, NoteListResponse, CreateNoteRequest, CreateNoteResponse, UpdateNoteRequest, UpdateNoteResponse, DeleteNoteRequest, DeleteNoteResponse};
pub use note_draft::{NoteDraftDetailResponse, CreateNoteDraftRequest, CreateNoteDraftResponse, UpdateNoteDraftRequest, UpdateNoteDraftResponse, NoteDraftDetailParams, DeleteNoteDraftParams, NoteDraftListParams, NoteDraftListItem, NoteDraftListResponse};
// pub use material::{AddMaterialRequest, AddMaterialResponse, DeleteMaterialRequest, DeleteMaterialResponse, MaterialListParams, MaterialListResponse, MaterialResponse};
// pub use augment_user::{AugmentLoginRequest, AugmentLoginData};
pub use prompt::{CreatePromptRequest, CreatePromptResponse, UpdatePromptRequest, UpdatePromptResponse, DeletePromptRequest, DeletePromptResponse, PromptListParams, PromptListResponse, PromptResponse};
pub use system_prompt::{CreateSystemPromptRequest, CreateSystemPromptResponse, UpdateSystemPromptRequest, UpdateSystemPromptResponse, DeleteSystemPromptRequest, DeleteSystemPromptResponse, SystemPromptListParams, SystemPromptListResponse, SystemPromptResponse};
pub use ai_usage::{CreateAiUsageRequest, CreateAiUsageResponse, GetAiUsageResponse};
