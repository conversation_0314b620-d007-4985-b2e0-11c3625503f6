use serde::{Deserialize, Serialize};

/// 用户注册请求数据结构
#[derive(Debug, Serialize, Deserialize)]
pub struct RegisterRequest {
    pub phone: String,
    pub password: String,
}

/// 用户注册响应数据
#[derive(Debug, Serialize, Deserialize)]
pub struct RegisterData {
    pub user_id: Option<String>,
    pub token: Option<String>,
}

/// 用户登录请求数据结构
#[derive(Debug, Serialize, Deserialize)]
pub struct LoginRequest {
    pub phone: String,
    pub password: String,
}

/// 用户登录响应数据
#[derive(Debug, Serialize, Deserialize)]
pub struct LoginData {
    pub user_id: String,
    pub phone: String,
    pub token: String,
}

/// 用户列表查询参数
#[derive(Debug, Deserialize)]
pub struct UserListParams {
    /// 页码，从1开始
    #[serde(default = "default_page")]
    pub page: u32,

    /// 每页数量
    #[serde(default = "default_page_size")]
    pub page_size: u32,
}

/// 默认页码
#[inline]
fn default_page() -> u32 {
    1
}

/// 默认每页数量
#[inline]
fn default_page_size() -> u32 {
    20
}

/// 用户信息响应
#[derive(Debug, Serialize)]
pub struct UserResponse {
    /// 用户ID
    pub id: u64,
    /// 手机号
    pub phone: String,
    /// 用户昵称
    pub nickname: Option<String>,
    /// 用户头像
    pub avatar: Option<String>,
    /// 创建时间
    pub created_at: String,
    /// 更新时间
    pub updated_at: String,
}

/// 用户列表响应
#[derive(Debug, Serialize)]
pub struct UserListResponse {
    /// 用户列表
    pub users: Vec<UserResponse>,
    /// 总数量
    pub total: u64,
    /// 当前页码
    pub page: u32,
    /// 每页数量
    pub page_size: u32,
}

impl UserResponse {
    /// 从MySQL用户模型转换为响应DTO
    pub fn from_mysql_model(user: &crate::models::mysql::MySqlUser) -> Self {
        Self {
            id: user.id,
            phone: user.phone.clone(),
            nickname: user.nickname.clone(),
            avatar: user.avatar.clone(),
            created_at: user.created_at.format("%Y-%m-%dT%H:%M:%SZ").to_string(),
            updated_at: user.updated_at.format("%Y-%m-%dT%H:%M:%SZ").to_string(),
        }
    }
}
