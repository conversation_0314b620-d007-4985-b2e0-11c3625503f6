use std::sync::Arc;
use sqlx::{MySql, Pool};
use thiserror::Error;

use crate::{
    config::AppConfig,
    db::utils::mysql_helper,
    db::DbConnections,
    models::mysql::MySqlUser,
};

/// MySQL用户服务错误类型
#[derive(Debug, Error)]
pub enum MySqlUserServiceError {
    #[error("数据库错误: {0}")]
    DatabaseError(#[from] sqlx::Error),

    #[error("用户已存在")]
    UserAlreadyExists,

    #[error("用户不存在")]
    UserNotFound,

    #[error("密码不匹配")]
    PasswordMismatch,

    #[error("数据库连接不可用")]
    DatabaseNotAvailable,
}

/// MySQL用户服务，处理用户相关的业务逻辑
pub struct MySqlUserService {
    pool: Arc<Pool<MySql>>,
}

impl MySqlUserService {
    /**
     * 创建新的MySQL用户服务实例
     *
     * @param db_connections 数据库连接管理器
     * @param config 应用配置
     * @return 返回 MySqlUserService 实例或错误
     */
    pub fn new(db_connections: &DbConnections, _config: &Arc<AppConfig>) -> Result<Self, MySqlUserServiceError> {
        // 获取MySQL连接池
        let pool = mysql_helper::get_mysql_pool(db_connections)
            .ok_or(MySqlUserServiceError::DatabaseNotAvailable)?;

        Ok(Self {
            pool: Arc::new(pool.clone()),
        })
    }

    /**
     * 注册新用户
     *
     * @param phone 用户手机号
     * @param password 用户密码（实际应用中应该对密码进行哈希处理）
     * @return 成功返回新创建的用户模型，失败返回错误
     */
    pub async fn register_user(&self, phone: String, password: String) -> Result<MySqlUser, MySqlUserServiceError> {
        // 检查用户是否已存在
        let existing_user = sqlx::query_as::<_, MySqlUser>(
            "SELECT * FROM users WHERE phone = ?"
        )
        .bind(&phone)
        .fetch_optional(&*self.pool)
        .await?;

        if existing_user.is_some() {
            return Err(MySqlUserServiceError::UserAlreadyExists);
        }

        // 在实际应用中，这里应该对密码进行哈希处理
        // 例如使用 bcrypt 或 argon2 等库
        // let hashed_password = hash_password(password)?;

        // 创建新用户
        let _new_user = MySqlUser::new(phone.clone(), password.clone());

        // 插入用户到数据库
        let user_id = sqlx::query(
            "INSERT INTO users (phone, password) VALUES (?, ?)"
        )
        .bind(&phone)
        .bind(&password)
        .execute(&*self.pool)
        .await?
        .last_insert_id();

        // 获取新创建的用户
        let user = sqlx::query_as::<_, MySqlUser>(
            "SELECT * FROM users WHERE id = ?"
        )
        .bind(user_id)
        .fetch_one(&*self.pool)
        .await?;

        Ok(user)
    }

    /**
     * 用户登录
     *
     * @param phone 用户手机号
     * @param password 用户密码
     * @return 成功返回用户模型，失败返回错误（用户不存在或密码错误）
     */
    pub async fn login(&self, phone: &str, password: &str) -> Result<MySqlUser, MySqlUserServiceError> {
        // 查找用户
        let user = sqlx::query_as::<_, MySqlUser>(
            "SELECT * FROM users WHERE phone = ?"
        )
        .bind(phone)
        .fetch_optional(&*self.pool)
        .await?
        .ok_or(MySqlUserServiceError::UserNotFound)?;

        // 验证密码（实际应用中应该比较哈希值）
        if user.password != password {
            return Err(MySqlUserServiceError::PasswordMismatch);
        }

        Ok(user)
    }

    /**
     * 根据手机号查找用户
     *
     * @param phone 用户手机号
     * @return 成功返回用户模型选项（如果存在），失败返回错误
     */
    pub async fn find_by_phone(&self, phone: &str) -> Result<Option<MySqlUser>, MySqlUserServiceError> {
        let user = sqlx::query_as::<_, MySqlUser>(
            "SELECT * FROM users WHERE phone = ?"
        )
        .bind(phone)
        .fetch_optional(&*self.pool)
        .await?;

        Ok(user)
    }

    /**
     * 根据ID查找用户
     *
     * @param id 用户ID
     * @return 成功返回用户模型选项（如果存在），失败返回错误
     */
    pub async fn find_by_id(&self, id: u64) -> Result<Option<MySqlUser>, MySqlUserServiceError> {
        let user = sqlx::query_as::<_, MySqlUser>(
            "SELECT * FROM users WHERE id = ?"
        )
        .bind(id)
        .fetch_optional(&*self.pool)
        .await?;

        Ok(user)
    }

    /**
     * 更新用户信息
     *
     * @param user 要更新的用户模型，必须包含 ID
     * @return 成功返回空元组，失败返回错误
     */
    pub async fn update_user(&self, user: &MySqlUser) -> Result<(), MySqlUserServiceError> {
        // 确保用户有ID
        if user.id == 0 {
            return Err(MySqlUserServiceError::UserNotFound);
        }

        // 更新用户信息
        sqlx::query(
            "UPDATE users SET phone = ?, password = ?, unionid = ?, nickname = ?, avatar = ? WHERE id = ?"
        )
        .bind(&user.phone)
        .bind(&user.password)
        .bind(&user.unionid)
        .bind(&user.nickname)
        .bind(&user.avatar)
        .bind(user.id)
        .execute(&*self.pool)
        .await?;

        Ok(())
    }

    /**
     * 根据微信unionid查找用户
     *
     * @param unionid 微信unionid
     * @return 成功返回用户模型选项（如果存在），失败返回错误
     */
    pub async fn find_by_unionid(&self, unionid: &str) -> Result<Option<MySqlUser>, MySqlUserServiceError> {
        let user = sqlx::query_as::<_, MySqlUser>(
            "SELECT * FROM users WHERE unionid = ?"
        )
        .bind(unionid)
        .fetch_optional(&*self.pool)
        .await?;

        Ok(user)
    }

    /**
     * 注册微信用户
     *
     * @param phone 用户手机号
     * @param password 用户密码
     * @param unionid 微信unionid
     * @param nickname 用户昵称
     * @param avatar 用户头像
     * @return 成功返回新创建的用户模型，失败返回错误
     */
    pub async fn register_wechat_user(
        &self,
        phone: String,
        password: String,
        unionid: String,
        nickname: String,
        avatar: Option<String>
    ) -> Result<MySqlUser, MySqlUserServiceError> {
        // 检查用户是否已存在（通过unionid）
        let existing_user = sqlx::query_as::<_, MySqlUser>(
            "SELECT * FROM users WHERE unionid = ?"
        )
        .bind(&unionid)
        .fetch_optional(&*self.pool)
        .await?;

        if existing_user.is_some() {
            return Err(MySqlUserServiceError::UserAlreadyExists);
        }

        // 检查手机号是否已存在
        let existing_user = sqlx::query_as::<_, MySqlUser>(
            "SELECT * FROM users WHERE phone = ?"
        )
        .bind(&phone)
        .fetch_optional(&*self.pool)
        .await?;

        if existing_user.is_some() {
            return Err(MySqlUserServiceError::UserAlreadyExists);
        }

        // 创建新用户
        let _new_user = MySqlUser::new_simple_wechat_user(
            phone.clone(),
            password.clone(),
            unionid.clone(),
            nickname.clone(),
            avatar.clone()
        );

        // 插入用户到数据库
        let user_id = sqlx::query(
            "INSERT INTO users (phone, password, unionid, nickname, headimgurl, avatar) VALUES (?, ?, ?, ?, ?, ?)"
        )
        .bind(&phone)
        .bind(&password)
        .bind(&unionid)
        .bind(&nickname)
        .bind(&avatar) // 使用avatar作为headimgurl
        .bind(&avatar) // 保持avatar字段兼容
        .execute(&*self.pool)
        .await?
        .last_insert_id();

        // 获取新创建的用户
        let user = sqlx::query_as::<_, MySqlUser>(
            "SELECT * FROM users WHERE id = ?"
        )
        .bind(user_id)
        .fetch_one(&*self.pool)
        .await?;

        Ok(user)
    }

    /**
     * 获取用户列表（分页）
     *
     * @param page 页码，从1开始
     * @param page_size 每页数量
     * @return 成功返回用户列表和总数，失败返回错误
     */
    pub async fn list_users(&self, page: u32, page_size: u32) -> Result<(Vec<MySqlUser>, u64), MySqlUserServiceError> {
        let offset = (page - 1) * page_size;

        // 获取总数
        let total: (i64,) = sqlx::query_as("SELECT COUNT(*) FROM users")
            .fetch_one(&*self.pool)
            .await?;
        let total = total.0 as u64;

        // 获取用户列表
        let users = sqlx::query_as::<_, MySqlUser>(
            "SELECT * FROM users ORDER BY created_at DESC LIMIT ? OFFSET ?"
        )
        .bind(page_size)
        .bind(offset)
        .fetch_all(&*self.pool)
        .await?;

        Ok((users, total))
    }
}
