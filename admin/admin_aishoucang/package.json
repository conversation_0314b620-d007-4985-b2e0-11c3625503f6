{"name": "admin_a<PERSON><PERSON>cang", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vitejs/plugin-vue": "^4.2.3", "axios": "^1.10.0", "element-plus": "^2.10.2", "pinia": "^3.0.3", "vite": "^4.5.0", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"prettier": "3.5.3", "vite-plugin-vue-devtools": "^7.7.7"}}