import { createRouter, createWebHistory } from 'vue-router'
import { isLoggedIn } from '@/api/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
      meta: { requiresGuest: true }
    },
    {
      path: '/',
      name: 'dashboard',
      component: () => import('../views/DashboardView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/users/list',
      name: 'userList',
      component: () => import('../views/UserListView.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/users/roles',
      name: 'userRoles',
      component: () => import('../views/AboutView.vue'), // 临时使用AboutView
      meta: { requiresAuth: true }
    },
    {
      path: '/content/list',
      name: 'contentList',
      component: () => import('../views/AboutView.vue'), // 临时使用AboutView
      meta: { requiresAuth: true }
    },
    {
      path: '/content/category',
      name: 'contentCategory',
      component: () => import('../views/AboutView.vue'), // 临时使用AboutView
      meta: { requiresAuth: true }
    },
    {
      path: '/analytics',
      name: 'analytics',
      component: () => import('../views/AboutView.vue'), // 临时使用AboutView
      meta: { requiresAuth: true }
    },
    {
      path: '/settings',
      name: 'settings',
      component: () => import('../views/AboutView.vue'), // 临时使用AboutView
      meta: { requiresAuth: true }
    },
    {
      path: '/about',
      name: 'about',
      component: () => import('../views/AboutView.vue'),
      meta: { requiresAuth: true }
    },
  ],
})

// 路由守卫
router.beforeEach((to, _from, next) => {
  const loggedIn = isLoggedIn()

  // 需要登录的页面
  if (to.meta.requiresAuth && !loggedIn) {
    next('/login')
    return
  }

  // 已登录用户访问登录页，重定向到首页
  if (to.meta.requiresGuest && loggedIn) {
    next('/')
    return
  }

  next()
})

export default router
