import { post } from './request'

/**
 * 管理员登录API
 * @param {object} data 登录数据
 * @param {string} data.account 管理员账号
 * @param {string} data.password 管理员密码
 * @returns {Promise}
 */
export const adminLogin = (data) => {
  return post('/admin/users/login', data)
}

/**
 * 管理员登出
 * @returns {Promise}
 */
export const adminLogout = () => {
  // 清除本地存储的用户信息和token
  localStorage.removeItem('admin_token')
  localStorage.removeItem('admin_user')
  return Promise.resolve()
}

/**
 * 获取当前登录的管理员信息
 * @returns {object|null}
 */
export const getCurrentAdmin = () => {
  const userStr = localStorage.getItem('admin_user')
  return userStr ? JSON.parse(userStr) : null
}

/**
 * 检查是否已登录
 * @returns {boolean}
 */
export const isLoggedIn = () => {
  const token = localStorage.getItem('admin_token')
  const user = localStorage.getItem('admin_user')
  return !!(token && user)
}

/**
 * 保存登录信息
 * @param {object} loginData 登录返回的数据
 */
export const saveLoginInfo = (loginData) => {
  localStorage.setItem('admin_token', loginData.token)
  localStorage.setItem('admin_user', JSON.stringify({
    user_id: loginData.user_id,
    account: loginData.account
  }))
}
