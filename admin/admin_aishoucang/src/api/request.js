import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const request = axios.create({
  baseURL: 'http://localhost:8080', // 后端API地址
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 从localStorage获取token
    const token = localStorage.getItem('admin_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    const { data } = response
    
    // 如果返回的状态码为200，说明接口请求成功，可以正常拿到数据
    if (response.status === 200) {
      // 根据后端返回的code判断
      if (data.code === 0) {
        return data
      } else {
        // 业务错误
        ElMessage.error(data.message || '请求失败')
        return Promise.reject(new Error(data.message || '请求失败'))
      }
    } else {
      ElMessage.error('网络错误')
      return Promise.reject(new Error('网络错误'))
    }
  },
  error => {
    console.error('响应错误:', error)
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          ElMessage.error('未授权，请重新登录')
          // 清除token并跳转到登录页
          localStorage.removeItem('admin_token')
          localStorage.removeItem('admin_user')
          window.location.href = '/login'
          break
        case 403:
          ElMessage.error('拒绝访问')
          break
        case 404:
          ElMessage.error('请求地址出错')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(data?.message || '请求失败')
      }
    } else if (error.code === 'ECONNABORTED') {
      ElMessage.error('请求超时')
    } else {
      ElMessage.error('网络错误')
    }
    
    return Promise.reject(error)
  }
)

/**
 * 统一的GET请求方法
 * @param {string} url 请求地址
 * @param {object} params 请求参数
 * @param {object} config 额外配置
 * @returns {Promise}
 */
export const get = (url, params = {}, config = {}) => {
  return request({
    method: 'GET',
    url,
    params,
    ...config
  })
}

/**
 * 统一的POST请求方法
 * @param {string} url 请求地址
 * @param {object} data 请求数据
 * @param {object} config 额外配置
 * @returns {Promise}
 */
export const post = (url, data = {}, config = {}) => {
  return request({
    method: 'POST',
    url,
    data,
    ...config
  })
}

/**
 * 统一的PUT请求方法
 * @param {string} url 请求地址
 * @param {object} data 请求数据
 * @param {object} config 额外配置
 * @returns {Promise}
 */
export const put = (url, data = {}, config = {}) => {
  return request({
    method: 'PUT',
    url,
    data,
    ...config
  })
}

/**
 * 统一的DELETE请求方法
 * @param {string} url 请求地址
 * @param {object} params 请求参数
 * @param {object} config 额外配置
 * @returns {Promise}
 */
export const del = (url, params = {}, config = {}) => {
  return request({
    method: 'DELETE',
    url,
    params,
    ...config
  })
}

export default request
