@import './base.css';

/* 全局样式重置和优化 */
* {
  box-sizing: border-box;
}

#app {
  margin: 0;
  padding: 0;
  font-weight: normal;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Element Plus 组件样式优化 */
.el-card {
  border: none !important;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08) !important;
}

.el-button {
  border-radius: 8px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
}

.el-button--primary {
  background: linear-gradient(135deg, #667eea, #764ba2) !important;
  border: none !important;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #5a67d8, #6b46c1) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4) !important;
}

.el-input__wrapper {
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
}

.el-input__wrapper:hover {
  box-shadow: 0 0 0 1px #667eea !important;
}

.el-input__wrapper.is-focus {
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.3) !important;
}

.el-dropdown-menu {
  border-radius: 12px !important;
  border: none !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12) !important;
  padding: 8px !important;
}

.el-dropdown-menu__item {
  border-radius: 8px !important;
  margin: 2px 0 !important;
  transition: all 0.3s ease !important;
}

.el-dropdown-menu__item:hover {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)) !important;
  color: #667eea !important;
}

.el-breadcrumb {
  font-weight: 500 !important;
}

.el-breadcrumb__item {
  color: #718096 !important;
}

.el-breadcrumb__item:last-child {
  color: #667eea !important;
}

.el-badge__content {
  background: linear-gradient(135deg, #f093fb, #f5576c) !important;
  border: none !important;
  font-weight: 600 !important;
}

/* 滚动条全局样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a67d8, #6b46c1);
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

/* 链接样式 */
a {
  text-decoration: none;
  color: #667eea;
  transition: all 0.3s ease;
}

a:hover {
  color: #5a67d8;
}

/* 文本选择样式 */
::selection {
  background: rgba(102, 126, 234, 0.2);
  color: #1a202c;
}

/* 焦点样式 */
:focus-visible {
  outline: 2px solid #667eea;
  outline-offset: 2px;
  border-radius: 4px;
}
